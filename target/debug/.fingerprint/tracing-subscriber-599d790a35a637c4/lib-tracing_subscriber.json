{"rustc": 15497389221046826682, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 8343665776428365373, "path": 17770270771451006161, "deps": [[1009387600818341822, "matchers", false, 8475594117895506548], [1017461770342116999, "sharded_slab", false, 6712428138300242882], [3722963349756955755, "once_cell", false, 11128816381147966684], [6048213226671835012, "smallvec", false, 10665109120087383571], [6981130804689348050, "tracing_serde", false, 1971660517043547066], [8606274917505247608, "tracing", false, 14518718483930457907], [8614575489689151157, "nu_ansi_term", false, 11376598350306665146], [9451456094439810778, "regex", false, 18240033615403063114], [9689903380558560274, "serde", false, 6793161948674590040], [10806489435541507125, "tracing_log", false, 8402313265932848354], [11033263105862272874, "tracing_core", false, 2636004985151416036], [12409575957772518135, "time", false, 14452914678492897678], [12427285511609802057, "thread_local", false, 15570933937480603672], [15367738274754116744, "serde_json", false, 13539151152161057597]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-599d790a35a637c4/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}