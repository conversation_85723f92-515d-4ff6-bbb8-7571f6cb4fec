{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private\", \"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 577721394348944849, "path": 17301405828534346599, "deps": [[40386456601120721, "percent_encoding", false, 1422238478359313970], [784494742817713399, "tower_service", false, 6653227714036533225], [1906322745568073236, "pin_project_lite", false, 1282854809380422486], [2517136641825875337, "sync_wrapper", false, 11785726561391257389], [3129130049864710036, "memchr", false, 8014856533345554509], [5695049318159433696, "tower", false, 248673119954485930], [7695812897323945497, "itoa", false, 1645729306886688316], [7712452662827335977, "tower_layer", false, 12960532446888941391], [7858942147296547339, "rustversion", false, 10535099639334957429], [8913795983780778928, "matchit", false, 2555226536557480490], [9010263965687315507, "http", false, 552879283891733865], [9689903380558560274, "serde", false, 6793161948674590040], [10229185211513642314, "mime", false, 11422750853343448006], [10629569228670356391, "futures_util", false, 15850491685942680162], [14084095096285906100, "http_body", false, 9838735962506736991], [15176407853393882315, "axum_core", false, 3206284164759870891], [16066129441945555748, "bytes", false, 7598836490931485274], [16900715236047033623, "http_body_util", false, 5594875166965524992]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-771d3c04954ae525/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}