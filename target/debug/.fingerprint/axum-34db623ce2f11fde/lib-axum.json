{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"headers\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 12074263998246110377, "profile": 11738465886959544573, "path": 2695021009374120339, "deps": [[40386456601120721, "percent_encoding", false, 1422238478359313970], [264090853244900308, "sync_wrapper", false, 17566868383067093378], [784494742817713399, "tower_service", false, 6653227714036533225], [1906322745568073236, "pin_project_lite", false, 1282854809380422486], [3129130049864710036, "memchr", false, 8014856533345554509], [3601586811267292532, "tower", false, 13616599210524162288], [4405182208873388884, "http", false, 2666357272146045440], [7414427314941361239, "hyper", false, 16097766036486165481], [7695812897323945497, "itoa", false, 1645729306886688316], [7712452662827335977, "tower_layer", false, 12960532446888941391], [8915503303801890683, "http_body", false, 13104665638293082233], [9293824762099617471, "axum_core", false, 14032300415424682063], [9678799920983747518, "matchit", false, 5910800940376130676], [9689903380558560274, "serde", false, 6793161948674590040], [10229185211513642314, "mime", false, 11422750853343448006], [10435729446543529114, "bitflags", false, 23942908202446972], [10629569228670356391, "futures_util", false, 15850491685942680162], [11946729385090170470, "async_trait", false, 7172890008290943547], [16066129441945555748, "bytes", false, 7598836490931485274], [16244562316228021087, "build_script_build", false, 5057936745581714015]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-34db623ce2f11fde/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}