{"rustc": 15497389221046826682, "features": "[\"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"deflate\", \"gzip\", \"hyper-rustls\", \"json\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-rustls\", \"tokio-util\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 11738465886959544573, "path": 12360358679216226155, "deps": [[40386456601120721, "percent_encoding", false, 1422238478359313970], [95042085696191081, "ipnet", false, 6126412534467748812], [126872836426101300, "async_compression", false, 6929246962066119854], [264090853244900308, "sync_wrapper", false, 17566868383067093378], [784494742817713399, "tower_service", false, 6653227714036533225], [1044435446100926395, "hyper_rustls", false, 12069064930262078151], [1288403060204016458, "tokio_util", false, 1740852340669449662], [1906322745568073236, "pin_project_lite", false, 1282854809380422486], [3150220818285335163, "url", false, 11696871892521108296], [3722963349756955755, "once_cell", false, 11128816381147966684], [4405182208873388884, "http", false, 2666357272146045440], [5986029879202738730, "log", false, 2740718825417204837], [7414427314941361239, "hyper", false, 16097766036486165481], [7620660491849607393, "futures_core", false, 17367435145200814220], [8915503303801890683, "http_body", false, 13104665638293082233], [9538054652646069845, "tokio", false, 2129010315477626780], [9689903380558560274, "serde", false, 6793161948674590040], [10229185211513642314, "mime", false, 11422750853343448006], [10629569228670356391, "futures_util", false, 15850491685942680162], [11107720164717273507, "system_configuration", false, 16239779228629524917], [11295624341523567602, "rustls", false, 3708715436363812284], [13809605890706463735, "h2", false, 1816996204349252649], [14564311161534545801, "encoding_rs", false, 1422440343107686997], [15367738274754116744, "serde_json", false, 13539151152161057597], [16066129441945555748, "bytes", false, 7598836490931485274], [16311359161338405624, "rustls_pemfile", false, 11354204518031172673], [16542808166767769916, "serde_urlencoded", false, 1493145760892808159], [16622232390123975175, "tokio_rustls", false, 7373596529072004232], [17652733826348741533, "webpki_roots", false, 11659144417367375372], [18066890886671768183, "base64", false, 15965221986944033837]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-a5147166a1a03035/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}