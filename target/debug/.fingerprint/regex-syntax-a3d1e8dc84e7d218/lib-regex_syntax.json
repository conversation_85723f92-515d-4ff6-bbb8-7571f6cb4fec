{"rustc": 15497389221046826682, "features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 7529137146482485884, "profile": 11738465886959544573, "path": 4354749700453370759, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-syntax-a3d1e8dc84e7d218/dep-lib-regex_syntax", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}