{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 10699556535754445188, "path": 3622169010033162278, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winnow-530d29697b64829d/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}