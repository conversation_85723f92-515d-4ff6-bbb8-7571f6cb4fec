{"rustc": 15497389221046826682, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 14534801088878213473, "path": 10162044332273838614, "deps": [[1213098572879462490, "json5_rs", false, 14915803955823082888], [1238778183371849706, "yaml_rust2", false, 333992672370711696], [2244620803250265856, "ron", false, 13825556094092107439], [2356429411733741858, "ini", false, 3298583583045447746], [6517602928339163454, "path<PERSON><PERSON>", false, 4072324473101369660], [8786711029710048183, "toml", false, 15298943455009978037], [9689903380558560274, "serde", false, 6793161948674590040], [11946729385090170470, "async_trait", false, 7172890008290943547], [13475460906694513802, "convert_case", false, 9320888762724338953], [14718834678227948963, "winnow", false, 10223233612681046532], [15367738274754116744, "serde_json", false, 13539151152161057597]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-bada37de7ce99f21/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}