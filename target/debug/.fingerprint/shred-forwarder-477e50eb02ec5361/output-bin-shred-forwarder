{"$message_type":"diagnostic","message":"unresolved import `super::proto::VersionedTransactionResponse`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src/grpc/broadcaster.rs","byte_start":120,"byte_end":162,"line_start":6,"line_end":6,"column_start":5,"column_end":47,"is_primary":true,"text":[{"text":"use super::proto::VersionedTransactionResponse;","highlight_start":5,"highlight_end":47}],"label":"no `VersionedTransactionResponse` in `grpc::proto`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m: unresolved import `super::proto::VersionedTransactionResponse`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/grpc/broadcaster.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse super::proto::VersionedTransactionResponse;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `VersionedTransactionResponse` in `grpc::proto`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `super::proto::VersionedTransactionResponse`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src/grpc/server.rs","byte_start":132,"byte_end":160,"line_start":6,"line_end":6,"column_start":20,"column_end":48,"is_primary":true,"text":[{"text":"use super::proto::{VersionedTransactionResponse, shred_forwarder_service_server::ShredForwarderServiceServer};","highlight_start":20,"highlight_end":48}],"label":"no `VersionedTransactionResponse` in `grpc::proto`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m: unresolved import `super::proto::VersionedTransactionResponse`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/grpc/server.rs:6:20\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse super::proto::{VersionedTransactionResponse, shred_forwarder_service_server::ShredForwarderServiceServer};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `VersionedTransactionResponse` in `grpc::proto`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved imports `super::proto::AccountSubscriptionRequest`, `super::proto::GetActiveSubscriptionsRequest`, `super::proto::GetActiveSubscriptionsResponse`, `super::proto::UpdateSubscriptionRequest`, `super::proto::UpdateSubscriptionResponse`, `super::proto::VersionedTransactionResponse`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src/grpc/service.rs","byte_start":273,"byte_end":299,"line_start":10,"line_end":10,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    AccountSubscriptionRequest, GetActiveSubscriptionsRequest, GetActiveSubscriptionsResponse,","highlight_start":5,"highlight_end":31}],"label":"no `AccountSubscriptionRequest` in `grpc::proto`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/grpc/service.rs","byte_start":301,"byte_end":330,"line_start":10,"line_end":10,"column_start":33,"column_end":62,"is_primary":true,"text":[{"text":"    AccountSubscriptionRequest, GetActiveSubscriptionsRequest, GetActiveSubscriptionsResponse,","highlight_start":33,"highlight_end":62}],"label":"no `GetActiveSubscriptionsRequest` in `grpc::proto`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/grpc/service.rs","byte_start":332,"byte_end":362,"line_start":10,"line_end":10,"column_start":64,"column_end":94,"is_primary":true,"text":[{"text":"    AccountSubscriptionRequest, GetActiveSubscriptionsRequest, GetActiveSubscriptionsResponse,","highlight_start":64,"highlight_end":94}],"label":"no `GetActiveSubscriptionsResponse` in `grpc::proto`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/grpc/service.rs","byte_start":368,"byte_end":393,"line_start":11,"line_end":11,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"    UpdateSubscriptionRequest, UpdateSubscriptionResponse, VersionedTransactionResponse,","highlight_start":5,"highlight_end":30}],"label":"no `UpdateSubscriptionRequest` in `grpc::proto`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/grpc/service.rs","byte_start":395,"byte_end":421,"line_start":11,"line_end":11,"column_start":32,"column_end":58,"is_primary":true,"text":[{"text":"    UpdateSubscriptionRequest, UpdateSubscriptionResponse, VersionedTransactionResponse,","highlight_start":32,"highlight_end":58}],"label":"no `UpdateSubscriptionResponse` in `grpc::proto`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/grpc/service.rs","byte_start":423,"byte_end":451,"line_start":11,"line_end":11,"column_start":60,"column_end":88,"is_primary":true,"text":[{"text":"    UpdateSubscriptionRequest, UpdateSubscriptionResponse, VersionedTransactionResponse,","highlight_start":60,"highlight_end":88}],"label":"no `VersionedTransactionResponse` in `grpc::proto`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m: unresolved imports `super::proto::AccountSubscriptionRequest`, `super::proto::GetActiveSubscriptionsRequest`, `super::proto::GetActiveSubscriptionsResponse`, `super::proto::UpdateSubscriptionRequest`, `super::proto::UpdateSubscriptionResponse`, `super::proto::VersionedTransactionResponse`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/grpc/service.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    AccountSubscriptionRequest, GetActiveSubscriptionsRequest, GetActiveSubscriptionsResponse,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `GetActiveSubscriptionsResponse` in `grpc::proto`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `GetActiveSubscriptionsRequest` in `grpc::proto`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `AccountSubscriptionRequest` in `grpc::proto`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    UpdateSubscriptionRequest, UpdateSubscriptionResponse, VersionedTransactionResponse,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `VersionedTransactionResponse` in `grpc::proto`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `UpdateSubscriptionResponse` in `grpc::proto`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `UpdateSubscriptionRequest` in `grpc::proto`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type `SubscribeAccountsStream` is not a member of trait `ShredForwarderService`","code":{"code":"E0437","explanation":"An associated type whose name does not match any of the associated types\nin the trait was used when implementing the trait.\n\nErroneous code example:\n\n```compile_fail,E0437\ntrait Foo {}\n\nimpl Foo for i32 {\n    type Bar = bool;\n}\n```\n\nTrait implementations can only implement associated types that are members of\nthe trait in question.\n\nThe solution to this problem is to remove the extraneous associated type:\n\n```\ntrait Foo {}\n\nimpl Foo for i32 {}\n```\n"},"level":"error","spans":[{"file_name":"src/grpc/service.rs","byte_start":3302,"byte_end":3351,"line_start":98,"line_end":98,"column_start":5,"column_end":54,"is_primary":true,"text":[{"text":"    type SubscribeAccountsStream = TransactionStream;","highlight_start":5,"highlight_end":54}],"label":"not a member of trait `ShredForwarderService`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0437]\u001b[0m\u001b[0m\u001b[1m: type `SubscribeAccountsStream` is not a member of trait `ShredForwarderService`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/grpc/service.rs:98:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m98\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    type SubscribeAccountsStream = TransactionStream;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot a member of trait `ShredForwarderService`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `subscribe_accounts` is not a member of trait `ShredForwarderService`","code":{"code":"E0407","explanation":"A definition of a method not in the implemented trait was given in a trait\nimplementation.\n\nErroneous code example:\n\n```compile_fail,E0407\ntrait Foo {\n    fn a();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn a() {}\n    fn b() {} // error: method `b` is not a member of trait `Foo`\n}\n```\n\nPlease verify you didn't misspell the method name and you used the correct\ntrait. First example:\n\n```\ntrait Foo {\n    fn a();\n    fn b();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn a() {}\n    fn b() {} // ok!\n}\n```\n\nSecond example:\n\n```\ntrait Foo {\n    fn a();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn a() {}\n}\n\nimpl Bar {\n    fn b() {}\n}\n```\n"},"level":"error","spans":[{"file_name":"src/grpc/service.rs","byte_start":3357,"byte_end":5568,"line_start":100,"line_end":161,"column_start":5,"column_end":6,"is_primary":true,"text":[{"text":"    async fn subscribe_accounts(","highlight_start":5,"highlight_end":33},{"text":"        &self,","highlight_start":1,"highlight_end":15},{"text":"        request: Request<AccountSubscriptionRequest>,","highlight_start":1,"highlight_end":54},{"text":"    ) -> Result<Response<Self::SubscribeAccountsStream>, Status> {","highlight_start":1,"highlight_end":67},{"text":"        let req = request.into_inner();","highlight_start":1,"highlight_end":40},{"text":"        let client_id = if req.client_id.is_empty() {","highlight_start":1,"highlight_end":54},{"text":"            format!(\"client_{}\", uuid::Uuid::new_v4())","highlight_start":1,"highlight_end":55},{"text":"        } else {","highlight_start":1,"highlight_end":17},{"text":"            req.client_id","highlight_start":1,"highlight_end":26},{"text":"        };","highlight_start":1,"highlight_end":11},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        info!(","highlight_start":1,"highlight_end":15},{"text":"            client_id = %client_id,","highlight_start":1,"highlight_end":36},{"text":"            accounts_count = req.accounts.len(),","highlight_start":1,"highlight_end":49},{"text":"            \"New account subscription request\"","highlight_start":1,"highlight_end":47},{"text":"        );","highlight_start":1,"highlight_end":11},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        if let Err(e) = self.validate_accounts(&req.accounts).await {","highlight_start":1,"highlight_end":70},{"text":"            error!(client_id = %client_id, error = %e, \"Account validation failed\");","highlight_start":1,"highlight_end":85},{"text":"            return Err(Status::invalid_argument(e.to_string()));","highlight_start":1,"highlight_end":65},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        let receiver = self.transaction_sender.subscribe();","highlight_start":1,"highlight_end":60},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        let subscription = ClientSubscription {","highlight_start":1,"highlight_end":48},{"text":"            client_id: client_id.clone(),","highlight_start":1,"highlight_end":42},{"text":"            accounts: req.accounts.clone(),","highlight_start":1,"highlight_end":44},{"text":"            sender: self.transaction_sender.clone(),","highlight_start":1,"highlight_end":53},{"text":"        };","highlight_start":1,"highlight_end":11},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        {","highlight_start":1,"highlight_end":10},{"text":"            let mut clients = self.clients.write().await;","highlight_start":1,"highlight_end":58},{"text":"            clients.insert(client_id.clone(), subscription);","highlight_start":1,"highlight_end":61},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        info!(","highlight_start":1,"highlight_end":15},{"text":"            client_id = %client_id,","highlight_start":1,"highlight_end":36},{"text":"            accounts = ?req.accounts,","highlight_start":1,"highlight_end":38},{"text":"            \"Client subscription registered\"","highlight_start":1,"highlight_end":45},{"text":"        );","highlight_start":1,"highlight_end":11},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        let client_accounts = req.accounts.clone();","highlight_start":1,"highlight_end":52},{"text":"        let stream = BroadcastStream::new(receiver).map(move |result| {","highlight_start":1,"highlight_end":72},{"text":"            match result {","highlight_start":1,"highlight_end":27},{"text":"                Ok(transaction) => {","highlight_start":1,"highlight_end":37},{"text":"                    // Check if transaction contains any of the client's subscribed accounts","highlight_start":1,"highlight_end":93},{"text":"                    if transaction_matches_accounts(&transaction, &client_accounts) {","highlight_start":1,"highlight_end":86},{"text":"                        Ok(transaction)","highlight_start":1,"highlight_end":40},{"text":"                    } else {","highlight_start":1,"highlight_end":29},{"text":"                        // Skip transactions that don't match by returning a special error","highlight_start":1,"highlight_end":91},{"text":"                        Err(Status::cancelled(\"Transaction filtered - no matching accounts\"))","highlight_start":1,"highlight_end":94},{"text":"                    }","highlight_start":1,"highlight_end":22},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"                Err(e) => {","highlight_start":1,"highlight_end":28},{"text":"                    warn!(error = %e, \"Broadcast stream error\");","highlight_start":1,"highlight_end":65},{"text":"                    Err(Status::internal(\"Stream error\"))","highlight_start":1,"highlight_end":58},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        });","highlight_start":1,"highlight_end":12},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        Ok(Response::new(Box::pin(stream)))","highlight_start":1,"highlight_end":44},{"text":"    }","highlight_start":1,"highlight_end":6}],"label":"not a member of trait `ShredForwarderService`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0407]\u001b[0m\u001b[0m\u001b[1m: method `subscribe_accounts` is not a member of trait `ShredForwarderService`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/grpc/service.rs:100:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn subscribe_accounts(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m101\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        &self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m102\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        request: Request<AccountSubscriptionRequest>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m103\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ) -> Result<Response<Self::SubscribeAccountsStream>, Status> {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m160\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Ok(Response::new(Box::pin(stream)))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot a member of trait `ShredForwarderService`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `update_subscription` is not a member of trait `ShredForwarderService`","code":{"code":"E0407","explanation":"A definition of a method not in the implemented trait was given in a trait\nimplementation.\n\nErroneous code example:\n\n```compile_fail,E0407\ntrait Foo {\n    fn a();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn a() {}\n    fn b() {} // error: method `b` is not a member of trait `Foo`\n}\n```\n\nPlease verify you didn't misspell the method name and you used the correct\ntrait. First example:\n\n```\ntrait Foo {\n    fn a();\n    fn b();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn a() {}\n    fn b() {} // ok!\n}\n```\n\nSecond example:\n\n```\ntrait Foo {\n    fn a();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn a() {}\n}\n\nimpl Bar {\n    fn b() {}\n}\n```\n"},"level":"error","spans":[{"file_name":"src/grpc/service.rs","byte_start":5574,"byte_end":7254,"line_start":163,"line_end":209,"column_start":5,"column_end":6,"is_primary":true,"text":[{"text":"    async fn update_subscription(","highlight_start":5,"highlight_end":34},{"text":"        &self,","highlight_start":1,"highlight_end":15},{"text":"        request: Request<UpdateSubscriptionRequest>,","highlight_start":1,"highlight_end":53},{"text":"    ) -> Result<Response<UpdateSubscriptionResponse>, Status> {","highlight_start":1,"highlight_end":64},{"text":"        let req = request.into_inner();","highlight_start":1,"highlight_end":40},{"text":"        let client_id = req.client_id;","highlight_start":1,"highlight_end":39},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        info!(","highlight_start":1,"highlight_end":15},{"text":"            client_id = %client_id,","highlight_start":1,"highlight_end":36},{"text":"            new_accounts_count = req.accounts.len(),","highlight_start":1,"highlight_end":53},{"text":"            \"Subscription update request\"","highlight_start":1,"highlight_end":42},{"text":"        );","highlight_start":1,"highlight_end":11},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        if let Err(e) = self.validate_accounts(&req.accounts).await {","highlight_start":1,"highlight_end":70},{"text":"            error!(client_id = %client_id, error = %e, \"Account validation failed\");","highlight_start":1,"highlight_end":85},{"text":"            return Ok(Response::new(UpdateSubscriptionResponse {","highlight_start":1,"highlight_end":65},{"text":"                success: false,","highlight_start":1,"highlight_end":32},{"text":"                message: e.to_string(),","highlight_start":1,"highlight_end":40},{"text":"                active_accounts_count: 0,","highlight_start":1,"highlight_end":42},{"text":"            }));","highlight_start":1,"highlight_end":17},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        let mut clients = self.clients.write().await;","highlight_start":1,"highlight_end":54},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        if let Some(subscription) = clients.get_mut(&client_id) {","highlight_start":1,"highlight_end":66},{"text":"            subscription.accounts = req.accounts.clone();","highlight_start":1,"highlight_end":58},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            info!(","highlight_start":1,"highlight_end":19},{"text":"                client_id = %client_id,","highlight_start":1,"highlight_end":40},{"text":"                accounts = ?req.accounts,","highlight_start":1,"highlight_end":42},{"text":"                \"Subscription updated successfully\"","highlight_start":1,"highlight_end":52},{"text":"            );","highlight_start":1,"highlight_end":15},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            Ok(Response::new(UpdateSubscriptionResponse {","highlight_start":1,"highlight_end":58},{"text":"                success: true,","highlight_start":1,"highlight_end":31},{"text":"                message: \"Subscription updated successfully\".to_string(),","highlight_start":1,"highlight_end":74},{"text":"                active_accounts_count: req.accounts.len() as u32,","highlight_start":1,"highlight_end":66},{"text":"            }))","highlight_start":1,"highlight_end":16},{"text":"        } else {","highlight_start":1,"highlight_end":17},{"text":"            warn!(client_id = %client_id, \"Client not found for subscription update\");","highlight_start":1,"highlight_end":87},{"text":"            Ok(Response::new(UpdateSubscriptionResponse {","highlight_start":1,"highlight_end":58},{"text":"                success: false,","highlight_start":1,"highlight_end":32},{"text":"                message: \"Client not found\".to_string(),","highlight_start":1,"highlight_end":57},{"text":"                active_accounts_count: 0,","highlight_start":1,"highlight_end":42},{"text":"            }))","highlight_start":1,"highlight_end":16},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"    }","highlight_start":1,"highlight_end":6}],"label":"not a member of trait `ShredForwarderService`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0407]\u001b[0m\u001b[0m\u001b[1m: method `update_subscription` is not a member of trait `ShredForwarderService`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/grpc/service.rs:163:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn update_subscription(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m164\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        &self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m165\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        request: Request<UpdateSubscriptionRequest>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ) -> Result<Response<UpdateSubscriptionResponse>, Status> {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m209\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot a member of trait `ShredForwarderService`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `get_active_subscriptions` is not a member of trait `ShredForwarderService`","code":{"code":"E0407","explanation":"A definition of a method not in the implemented trait was given in a trait\nimplementation.\n\nErroneous code example:\n\n```compile_fail,E0407\ntrait Foo {\n    fn a();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn a() {}\n    fn b() {} // error: method `b` is not a member of trait `Foo`\n}\n```\n\nPlease verify you didn't misspell the method name and you used the correct\ntrait. First example:\n\n```\ntrait Foo {\n    fn a();\n    fn b();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn a() {}\n    fn b() {} // ok!\n}\n```\n\nSecond example:\n\n```\ntrait Foo {\n    fn a();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn a() {}\n}\n\nimpl Bar {\n    fn b() {}\n}\n```\n"},"level":"error","spans":[{"file_name":"src/grpc/service.rs","byte_start":7260,"byte_end":8009,"line_start":211,"line_end":231,"column_start":5,"column_end":6,"is_primary":true,"text":[{"text":"    async fn get_active_subscriptions(","highlight_start":5,"highlight_end":39},{"text":"        &self,","highlight_start":1,"highlight_end":15},{"text":"        request: Request<GetActiveSubscriptionsRequest>,","highlight_start":1,"highlight_end":57},{"text":"    ) -> Result<Response<GetActiveSubscriptionsResponse>, Status> {","highlight_start":1,"highlight_end":68},{"text":"        let req = request.into_inner();","highlight_start":1,"highlight_end":40},{"text":"        let client_id = req.client_id;","highlight_start":1,"highlight_end":39},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        let clients = self.clients.read().await;","highlight_start":1,"highlight_end":49},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        if let Some(subscription) = clients.get(&client_id) {","highlight_start":1,"highlight_end":62},{"text":"            Ok(Response::new(GetActiveSubscriptionsResponse {","highlight_start":1,"highlight_end":62},{"text":"                accounts: subscription.accounts.clone(),","highlight_start":1,"highlight_end":57},{"text":"                total_accounts: subscription.accounts.len() as u32,","highlight_start":1,"highlight_end":68},{"text":"            }))","highlight_start":1,"highlight_end":16},{"text":"        } else {","highlight_start":1,"highlight_end":17},{"text":"            Ok(Response::new(GetActiveSubscriptionsResponse {","highlight_start":1,"highlight_end":62},{"text":"                accounts: vec![],","highlight_start":1,"highlight_end":34},{"text":"                total_accounts: 0,","highlight_start":1,"highlight_end":35},{"text":"            }))","highlight_start":1,"highlight_end":16},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"    }","highlight_start":1,"highlight_end":6}],"label":"not a member of trait `ShredForwarderService`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0407]\u001b[0m\u001b[0m\u001b[1m: method `get_active_subscriptions` is not a member of trait `ShredForwarderService`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/grpc/service.rs:211:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m211\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_active_subscriptions(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m212\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        &self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m213\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        request: Request<GetActiveSubscriptionsRequest>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m214\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ) -> Result<Response<GetActiveSubscriptionsResponse>, Status> {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m231\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot a member of trait `ShredForwarderService`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: could not find `prost_types` in the list of imported crates","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/Projects/shreds-decoder/target/debug/build/shred-forwarder-83c12794f361a830/out/shred_forwarder.rs","byte_start":466,"byte_end":477,"line_start":14,"line_end":14,"column_start":45,"column_end":56,"is_primary":true,"text":[{"text":"    pub timestamp: ::core::option::Option<::prost_types::Timestamp>,","highlight_start":45,"highlight_end":56}],"label":"could not find `prost_types` in the list of imported crates","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: could not find `prost_types` in the list of imported crates\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/Projects/shreds-decoder/target/debug/build/shred-forwarder-83c12794f361a830/out/shred_forwarder.rs:14:45\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub timestamp: ::core::option::Option<::prost_types::Timestamp>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `prost_types` in the list of imported crates\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: could not find `prost_types` in the list of imported crates","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/Projects/shreds-decoder/target/debug/build/shred-forwarder-83c12794f361a830/out/shred_forwarder.rs","byte_start":1003,"byte_end":1014,"line_start":29,"line_end":29,"column_start":47,"column_end":58,"is_primary":true,"text":[{"text":"    pub received_at: ::core::option::Option<::prost_types::Timestamp>,","highlight_start":47,"highlight_end":58}],"label":"could not find `prost_types` in the list of imported crates","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: could not find `prost_types` in the list of imported crates\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/Projects/shreds-decoder/target/debug/build/shred-forwarder-83c12794f361a830/out/shred_forwarder.rs:29:47\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m29\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub received_at: ::core::option::Option<::prost_types::Timestamp>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `prost_types` in the list of imported crates\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated type `SubscribeAccountsStream` not found for `Self`","code":{"code":"E0220","explanation":"The associated type used was not defined in the trait.\n\nErroneous code example:\n\n```compile_fail,E0220\ntrait T1 {\n    type Bar;\n}\n\ntype Foo = T1<F=i32>; // error: associated type `F` not found for `T1`\n\n// or:\n\ntrait T2 {\n    type Bar;\n\n    // error: Baz is used but not declared\n    fn return_bool(&self, _: &Self::Bar, _: &Self::Baz) -> bool;\n}\n```\n\nMake sure that you have defined the associated type in the trait body.\nAlso, verify that you used the right trait or you didn't misspell the\nassociated type name. Example:\n\n```\ntrait T1 {\n    type Bar;\n}\n\ntype Foo = T1<Bar=i32>; // ok!\n\n// or:\n\ntrait T2 {\n    type Bar;\n    type Baz; // we declare `Baz` in our trait.\n\n    // and now we can use it here:\n    fn return_bool(&self, _: &Self::Bar, _: &Self::Baz) -> bool;\n}\n```\n"},"level":"error","spans":[{"file_name":"src/grpc/service.rs","byte_start":3486,"byte_end":3509,"line_start":103,"line_end":103,"column_start":32,"column_end":55,"is_primary":true,"text":[{"text":"    ) -> Result<Response<Self::SubscribeAccountsStream>, Status> {","highlight_start":32,"highlight_end":55}],"label":"there is a similarly named associated type `SubscribeEntriesStream` in the trait `ShredstreamProxy`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0220]\u001b[0m\u001b[0m\u001b[1m: associated type `SubscribeAccountsStream` not found for `Self`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/grpc/service.rs:103:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m103\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ) -> Result<Response<Self::SubscribeAccountsStream>, Status> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthere is a similarly named associated type `SubscribeEntriesStream` in the trait `ShredstreamProxy`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"not all trait items implemented, missing: `ping`, `SubscribeStream`, `subscribe`","code":{"code":"E0046","explanation":"Items are missing in a trait implementation.\n\nErroneous code example:\n\n```compile_fail,E0046\ntrait Foo {\n    fn foo();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {}\n// error: not all trait items implemented, missing: `foo`\n```\n\nWhen trying to make some type implement a trait `Foo`, you must, at minimum,\nprovide implementations for all of `Foo`'s required methods (meaning the\nmethods that do not have default implementations), as well as any required\ntrait items like associated types or constants. Example:\n\n```\ntrait Foo {\n    fn foo();\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    fn foo() {} // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"src/grpc/service.rs","byte_start":3239,"byte_end":3295,"line_start":97,"line_end":97,"column_start":1,"column_end":57,"is_primary":true,"text":[{"text":"impl ShredForwarderService for ShredForwarderServiceImpl {","highlight_start":1,"highlight_end":57}],"label":"missing `ping`, `SubscribeStream`, `subscribe` in implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/Projects/shreds-decoder/target/debug/build/shred-forwarder-83c12794f361a830/out/shred_forwarder.rs","byte_start":7040,"byte_end":7217,"line_start":188,"line_end":191,"column_start":9,"column_end":87,"is_primary":false,"text":[{"text":"        async fn ping(","highlight_start":9,"highlight_end":23},{"text":"            &self,","highlight_start":1,"highlight_end":19},{"text":"            request: tonic::Request<super::PingRequest>,","highlight_start":1,"highlight_end":57},{"text":"        ) -> std::result::Result<tonic::Response<super::PongResponse>, tonic::Status>;","highlight_start":1,"highlight_end":87}],"label":"`ping` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/Projects/shreds-decoder/target/debug/build/shred-forwarder-83c12794f361a830/out/shred_forwarder.rs","byte_start":7295,"byte_end":7507,"line_start":193,"line_end":197,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"        type SubscribeStream: tonic::codegen::tokio_stream::Stream<","highlight_start":9,"highlight_end":68},{"text":"                Item = std::result::Result<super::SubscribeResponse, tonic::Status>,","highlight_start":1,"highlight_end":85},{"text":"            >","highlight_start":1,"highlight_end":14},{"text":"            + std::marker::Send","highlight_start":1,"highlight_end":32},{"text":"            + 'static;","highlight_start":1,"highlight_end":22}],"label":"`SubscribeStream` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/Projects/shreds-decoder/target/debug/build/shred-forwarder-83c12794f361a830/out/shred_forwarder.rs","byte_start":7517,"byte_end":7706,"line_start":198,"line_end":201,"column_start":9,"column_end":89,"is_primary":false,"text":[{"text":"        async fn subscribe(","highlight_start":9,"highlight_end":28},{"text":"            &self,","highlight_start":1,"highlight_end":19},{"text":"            request: tonic::Request<super::SubscribeRequest>,","highlight_start":1,"highlight_end":62},{"text":"        ) -> std::result::Result<tonic::Response<Self::SubscribeStream>, tonic::Status>;","highlight_start":1,"highlight_end":89}],"label":"`subscribe` from trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"implement the missing item: `fn ping(&'life0 self, _: tonic::Request<PingRequest>) -> Pin<Box<(dyn std::future::Future<Output = Result<tonic::Response<PongResponse>, tonic::Status>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"src/grpc/service.rs","byte_start":8010,"byte_end":8010,"line_start":232,"line_end":232,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn ping(&'life0 self, _: tonic::Request<PingRequest>) -> Pin<Box<(dyn std::future::Future<Output = Result<tonic::Response<PongResponse>, tonic::Status>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `type SubscribeStream = /* Type */;`","code":null,"level":"help","spans":[{"file_name":"src/grpc/service.rs","byte_start":8010,"byte_end":8010,"line_start":232,"line_end":232,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"type SubscribeStream = /* Type */;\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null},{"message":"implement the missing item: `fn subscribe(&'life0 self, _: tonic::Request<SubscribeRequest>) -> Pin<Box<(dyn std::future::Future<Output = Result<tonic::Response<<Self as ShredForwarderService>::SubscribeStream>, tonic::Status>> + Send + 'async_trait)>> { todo!() }`","code":null,"level":"help","spans":[{"file_name":"src/grpc/service.rs","byte_start":8010,"byte_end":8010,"line_start":232,"line_end":232,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"}","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"fn subscribe(&'life0 self, _: tonic::Request<SubscribeRequest>) -> Pin<Box<(dyn std::future::Future<Output = Result<tonic::Response<<Self as ShredForwarderService>::SubscribeStream>, tonic::Status>> + Send + 'async_trait)>> { todo!() }\n","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0046]\u001b[0m\u001b[0m\u001b[1m: not all trait items implemented, missing: `ping`, `SubscribeStream`, `subscribe`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/grpc/service.rs:97:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m97\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0mimpl ShredForwarderService for ShredForwarderServiceImpl {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `ping`, `SubscribeStream`, `subscribe` in implementation\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/Users/<USER>/Projects/shreds-decoder/target/debug/build/shred-forwarder-83c12794f361a830/out/shred_forwarder.rs:188:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        async fn ping(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            &self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            request: tonic::Request<super::PingRequest>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m191\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ) -> std::result::Result<tonic::Response<super::PongResponse>, tonic::Status>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|______________________________________________________________________________________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m`ping` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        /// Server streaming response type for the Subscribe method.\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m193\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        type SubscribeStream: tonic::codegen::tokio_stream::Stream<\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Item = std::result::Result<super::SubscribeResponse, tonic::Status>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m195\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            >\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m196\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            + std::marker::Send\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m197\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            + 'static;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_____________________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m`SubscribeStream` from trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m198\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        async fn subscribe(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            &self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m200\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            request: tonic::Request<super::SubscribeRequest>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m201\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ) -> std::result::Result<tonic::Response<Self::SubscribeStream>, tonic::Status>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|________________________________________________________________________________________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m`subscribe` from trait\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 11 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 11 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0046, E0220, E0407, E0432, E0433, E0437.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0046, E0220, E0407, E0432, E0433, E0437.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0046`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0046`.\u001b[0m\n"}
