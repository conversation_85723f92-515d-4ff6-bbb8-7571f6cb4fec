use anyhow::Result;
use tonic::Request;

use shred_forwarder::grpc::proto::{
    PingRequest, SubscribeRequest,
    shred_forwarder_service_client::ShredForwarderServiceClient,
    versioned_message::MessageType,
};

#[tokio::main]
async fn main() -> Result<()> {
    let mut client = ShredForwarderServiceClient::connect("http://127.0.0.1:50051").await?;

    println!("Testing Ping...");
    let ping_request = Request::new(PingRequest { count: 42 });
    let ping_response = client.ping(ping_request).await?;
    println!("Ping response: {:?}", ping_response.into_inner());

    println!("\nTesting Subscribe with detailed transaction info...");
    let subscribe_request = Request::new(SubscribeRequest {});
    let mut stream = client.subscribe(subscribe_request).await?.into_inner();

    println!("Listening for transactions...");
    let mut count = 0;
    while let Some(response) = stream.message().await? {
        count += 1;
        
        if let Some(ref tx) = response.transaction {
            println!("\n=== Transaction #{} ===", count);
            println!("Slot: {}", response.slot);
            println!("Entry Index: {}", response.entry_index);
            println!("Transaction Index: {}", response.transaction_index);
            println!("Signatures Count: {}", tx.signatures.len());
            
            // Show first signature if available
            if !tx.signatures.is_empty() {
                let sig_hex = hex::encode(&tx.signatures[0]);
                println!("First Signature: {}...{}", &sig_hex[..16], &sig_hex[sig_hex.len()-16..]);
            }
            
            if let Some(ref message) = tx.message {
                if let Some(ref msg_type) = message.message_type {
                    match msg_type {
                        MessageType::Legacy(legacy) => {
                            println!("Message Type: Legacy");
                            if let Some(ref header) = legacy.header {
                                println!("  Required Signatures: {}", header.num_required_signatures);
                                println!("  Readonly Signed: {}", header.num_readonly_signed_accounts);
                                println!("  Readonly Unsigned: {}", header.num_readonly_unsigned_accounts);
                            }
                            println!("  Account Keys: {}", legacy.account_keys.len());
                            println!("  Instructions: {}", legacy.instructions.len());
                            
                            // Show instruction details
                            for (i, inst) in legacy.instructions.iter().enumerate() {
                                println!("    Instruction {}: program_id_index={}, accounts={}, data_len={}", 
                                    i, inst.program_id_index, inst.accounts.len(), inst.data.len());
                            }
                        }
                        MessageType::V0(v0) => {
                            println!("Message Type: V0");
                            if let Some(ref header) = v0.header {
                                println!("  Required Signatures: {}", header.num_required_signatures);
                                println!("  Readonly Signed: {}", header.num_readonly_signed_accounts);
                                println!("  Readonly Unsigned: {}", header.num_readonly_unsigned_accounts);
                            }
                            println!("  Account Keys: {}", v0.account_keys.len());
                            println!("  Instructions: {}", v0.instructions.len());
                            println!("  Address Table Lookups: {}", v0.address_table_lookups.len());
                        }
                    }
                }
            }
            
            println!("Received At: {:?}", response.received_at);
            println!("Processing Time: {} nanos", response.processing_time_nanos);
        } else {
            println!("Transaction #{}: No transaction data", count);
        }

        if count >= 3 {
            println!("\nReceived 3 transactions, stopping...");
            break;
        }
    }

    Ok(())
}
