use anyhow::Result;
use tonic::Request;

use shred_forwarder::grpc::proto::{
    PingRequest, SubscribeRequest, shred_forwarder_service_client::ShredForwarderServiceClient,
};

#[tokio::main]
async fn main() -> Result<()> {
    let mut client = ShredForwarderServiceClient::connect("http://127.0.0.1:50051").await?;

    println!("Testing Ping...");
    let ping_request = Request::new(PingRequest { count: 42 });
    let ping_response = client.ping(ping_request).await?;
    println!("Ping response: {:?}", ping_response.into_inner());

    println!("Testing Subscribe...");
    let subscribe_request = Request::new(SubscribeRequest {});
    let mut stream = client.subscribe(subscribe_request).await?.into_inner();

    println!("Listening for transactions...");
    let mut count = 0;
    while let Some(response) = stream.message().await? {
        count += 1;

        let transaction_info = if let Some(ref tx) = response.transaction {
            format!(
                "signatures_count={}, message_type={}",
                tx.signatures.len(),
                if tx.message.as_ref().and_then(|m| m.message_type.as_ref()).is_some() {
                    match tx.message.as_ref().unwrap().message_type.as_ref().unwrap() {
                        shred_forwarder::grpc::proto::versioned_message::MessageType::Legacy(_) => "Legacy",
                        shred_forwarder::grpc::proto::versioned_message::MessageType::V0(_) => "V0",
                    }
                } else {
                    "Unknown"
                }
            )
        } else {
            "No transaction data".to_string()
        };

        println!(
            "Transaction #{}: slot={}, entry_index={}, transaction_index={}, {}, received_at={:?}, processing_time_nanos={}",
            count,
            response.slot,
            response.entry_index,
            response.transaction_index,
            transaction_info,
            response.received_at,
            response.processing_time_nanos
        );

        if count >= 5 {
            println!("Received 5 transactions, stopping...");
            break;
        }
    }

    Ok(())
}
