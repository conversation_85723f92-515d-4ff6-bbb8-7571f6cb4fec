[package]
name = "shred-forwarder"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow = "1.0.98"
bincode = "1.3.3"
config = "0.15.11"
hex = "0.4.3"
prost = "0.13.3"
prost-types = "0.13.3"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
solana-entry = "2.2.1"
solana-stream-sdk = "0.2.5"
solana-transaction = "2.2.2"
thiserror = "2.0.12"
tokio = "1.45.1"
tokio-stream = { version = "0.1.17", features = ["sync"] }
tonic = "0.13.1"
tonic-reflection = "0.13.1"
tracing = "0.1.41"
uuid = { version = "1.11.0", features = ["v4"] }
tracing-subscriber = { version = "0.3.19", features = [
    "env-filter",
    "json",
    "time",
] }

[build-dependencies]
tonic-build = "0.13.1"

[[example]]
name = "test_client"
path = "examples/test_client.rs"

[[example]]
name = "detailed_client"
path = "examples/detailed_client.rs"
