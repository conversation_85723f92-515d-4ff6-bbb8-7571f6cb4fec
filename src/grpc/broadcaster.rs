use std::time::{Instant, SystemTime};
use tokio::sync::broadcast;
use tracing::error;

use solana_transaction::versioned::VersionedTransaction;

use super::proto::SubscribeResponse;
use crate::types::{GrpcError, GrpcR<PERSON>ult};
use crate::utils::transaction::convert_to_proto_transaction;

pub struct TransactionBroadcaster {
    sender: broadcast::Sender<SubscribeResponse>,
}

impl TransactionBroadcaster {
    pub fn new(sender: broadcast::Sender<SubscribeResponse>) -> Self {
        Self { sender }
    }

    pub async fn broadcast_transaction(
        &self,
        transaction: &VersionedTransaction,
        slot: u64,
        entry_index: u32,
        transaction_index: u32,
        received_at: SystemTime,
        processing_start: Instant,
    ) -> GrpcResult<()> {
        let proto_transaction = match convert_to_proto_transaction(transaction) {
            Ok(proto_tx) => proto_tx,
            Err(e) => {
                error!(
                    slot = slot,
                    entry_index = entry_index,
                    transaction_index = transaction_index,
                    error = %e,
                    "Failed to convert transaction to proto"
                );
                return Err(GrpcError::Service {
                    message: format!("Transaction conversion failed: {}", e),
                });
            }
        };

        let processing_time_nanos = processing_start.elapsed().as_nanos() as u64;

        let response = SubscribeResponse {
            slot,
            entry_index,
            transaction_index,
            transaction: Some(proto_transaction),
            received_at: Some(prost_types::Timestamp::from(received_at)),
            processing_time_nanos,
        };

        match self.sender.send(response) {
            Ok(_) => Ok(()),
            Err(_) => Ok(()),
        }
    }
}
