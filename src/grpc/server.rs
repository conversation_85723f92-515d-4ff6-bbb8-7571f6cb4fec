use std::net::SocketAddr;
use tokio::sync::broadcast;
use tonic::transport::Server;
use tracing::{error, info};

use super::proto::{SubscribeResponse, shred_forwarder_service_server::ShredForwarderServiceServer};
use super::service::ShredForwarderServiceImpl;
use crate::config::GrpcConfig;
use crate::types::{GrpcError, GrpcResult};

pub struct GrpcServer {
    config: GrpcConfig,
    service: ShredForwarderServiceImpl,
}

impl GrpcServer {
    pub fn new(config: GrpcConfig, allowed_accounts: Vec<String>) -> Self {
        let service = ShredForwarderServiceImpl::new(config.clone(), allowed_accounts);

        Self { config, service }
    }

    pub fn get_transaction_sender(&self) -> broadcast::Sender<SubscribeResponse> {
        self.service.get_transaction_sender()
    }

    pub async fn start(&self) -> GrpcResult<()> {
        let bind_address = self.config.bind_address.clone();
        let port = self.config.port;

        let addr: SocketAddr =
            format!("{}:{}", bind_address, port)
                .parse()
                .map_err(|e| GrpcError::ServerStartFailed {
                    message: format!("Invalid bind address: {}", e),
                })?;

        info!(
            bind_address = %bind_address,
            port = port,
            addr = %addr,
            "Starting gRPC server"
        );

        let service_server = ShredForwarderServiceServer::new(self.service.clone());

        let mut server_builder = Server::builder().tcp_nodelay(self.config.tcp_nodelay);

        if let Some(keepalive_secs) = self.config.tcp_keepalive_seconds {
            server_builder = server_builder.tcp_keepalive(Some(std::time::Duration::from_secs(keepalive_secs)));
        }

        if let Some(interval_secs) = self.config.http2_keepalive_interval_seconds {
            server_builder =
                server_builder.http2_keepalive_interval(Some(std::time::Duration::from_secs(interval_secs)));
        }

        if let Some(timeout_secs) = self.config.http2_keepalive_timeout_seconds {
            server_builder = server_builder.http2_keepalive_timeout(Some(std::time::Duration::from_secs(timeout_secs)));
        }

        let server = server_builder.add_service(service_server);

        info!(addr = %addr, "gRPC server listening");

        if let Err(e) = server.serve(addr).await {
            error!(error = %e, "gRPC server failed");
            return Err(GrpcError::ServerStartFailed {
                message: format!("Server failed to start: {}", e),
            });
        }

        Ok(())
    }
}
