use std::collections::VecDeque;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::{Arc, Mutex};
use tracing::{debug, warn};

pub struct MemoryPool<T> {
    pool: Arc<Mutex<VecDeque<T>>>,
    max_size: usize,
    current_size: Arc<AtomicUsize>,
    allocations: AtomicUsize,
    pool_hits: AtomicUsize,
    pool_misses: AtomicUsize,
}

impl<T: Default> MemoryPool<T> {
    pub fn new(initial_size: usize, max_size: usize) -> Self {
        let mut pool = VecDeque::with_capacity(initial_size);

        for _ in 0..initial_size {
            pool.push_back(T::default());
        }

        Self {
            pool: Arc::new(Mutex::new(pool)),
            max_size,
            current_size: Arc::new(AtomicUsize::new(initial_size)),
            allocations: AtomicUsize::new(0),
            pool_hits: AtomicUsize::new(0),
            pool_misses: AtomicUsize::new(0),
        }
    }

    pub fn get(&self) -> PooledItem<T> {
        self.allocations.fetch_add(1, Ordering::Relaxed);

        if let Ok(mut pool) = self.pool.try_lock() {
            if let Some(item) = pool.pop_front() {
                self.current_size.fetch_sub(1, Ordering::Relaxed);
                self.pool_hits.fetch_add(1, Ordering::Relaxed);

                return PooledItem {
                    item: Some(item),
                    pool: Arc::clone(&self.pool),
                    max_size: self.max_size,
                    current_size: Arc::clone(&self.current_size),
                };
            }
        }

        self.pool_misses.fetch_add(1, Ordering::Relaxed);

        PooledItem {
            item: Some(T::default()),
            pool: Arc::clone(&self.pool),
            max_size: self.max_size,
            current_size: Arc::clone(&self.current_size),
        }
    }

    pub fn stats(&self) -> PoolStats {
        PoolStats {
            current_size: self.current_size.load(Ordering::Relaxed),
            max_size: self.max_size,
            total_allocations: self.allocations.load(Ordering::Relaxed),
            pool_hits: self.pool_hits.load(Ordering::Relaxed),
            pool_misses: self.pool_misses.load(Ordering::Relaxed),
        }
    }
}

pub struct PooledItem<T> {
    item: Option<T>,
    pool: Arc<Mutex<VecDeque<T>>>,
    max_size: usize,
    current_size: Arc<AtomicUsize>,
}

impl<T> PooledItem<T> {}

impl<T> Drop for PooledItem<T> {
    fn drop(&mut self) {
        if let Some(item) = self.item.take() {
            let current = self.current_size.load(Ordering::Relaxed);

            if current < self.max_size {
                if let Ok(mut pool) = self.pool.try_lock() {
                    pool.push_back(item);
                    self.current_size.fetch_add(1, Ordering::Relaxed);
                } else {
                    warn!("Failed to return item to pool: lock contention");
                }
            } else {
                debug!("Pool at max capacity, dropping item");
            }
        }
    }
}

#[derive(Debug, Clone)]
pub struct PoolStats {
    pub current_size: usize,
    pub max_size: usize,
    pub total_allocations: usize,
    pub pool_hits: usize,
    pub pool_misses: usize,
}

impl PoolStats {
    pub fn hit_rate(&self) -> f64 {
        if self.total_allocations == 0 {
            0.0
        } else {
            self.pool_hits as f64 / self.total_allocations as f64
        }
    }
}

pub type BufferPool = MemoryPool<Vec<u8>>;
pub type PooledBuffer = PooledItem<Vec<u8>>;

static BUFFER_POOL: std::sync::OnceLock<BufferPool> = std::sync::OnceLock::new();

pub fn init_pools(config: &MemoryPoolConfig) {
    BUFFER_POOL.get_or_init(|| BufferPool::new(config.buffer_pool_initial_size, config.buffer_pool_max_size));
}

pub fn get_buffer_pool() -> &'static BufferPool {
    BUFFER_POOL.get_or_init(|| BufferPool::new(50, 500))
}

pub fn get_pooled_buffer() -> PooledBuffer {
    get_buffer_pool().get()
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct MemoryPoolConfig {
    pub transaction_pool_initial_size: usize,
    pub transaction_pool_max_size: usize,
    pub buffer_pool_initial_size: usize,
    pub buffer_pool_max_size: usize,
    pub enable_pool_stats_logging: bool,
    pub stats_log_interval_seconds: u64,
}

impl Default for MemoryPoolConfig {
    fn default() -> Self {
        Self {
            transaction_pool_initial_size: 100,
            transaction_pool_max_size: 1000,
            buffer_pool_initial_size: 50,
            buffer_pool_max_size: 500,
            enable_pool_stats_logging: false,
            stats_log_interval_seconds: 60,
        }
    }
}

pub fn log_pool_stats() {
    let buf_stats = get_buffer_pool().stats();
    debug!(
        current_size = buf_stats.current_size,
        max_size = buf_stats.max_size,
        total_allocations = buf_stats.total_allocations,
        pool_hits = buf_stats.pool_hits,
        pool_misses = buf_stats.pool_misses,
        hit_rate = %format!("{:.2}%", buf_stats.hit_rate() * 100.0),
        "Buffer pool statistics"
    );
}
