use solana_transaction::versioned::VersionedTransaction;

use crate::grpc::proto::{
    CompiledInstruction as ProtoCompiledInstruction, LegacyMessage as ProtoLegacyMessage,
    MessageHeader as ProtoMessageHeader, VersionedMessage as ProtoVersionedMessage,
    VersionedTransaction as ProtoVersionedTransaction,
};
use crate::types::GrpcResult;

pub fn convert_to_proto_transaction(transaction: &VersionedTransaction) -> GrpcResult<ProtoVersionedTransaction> {
    let signatures = transaction.signatures.iter().map(|sig| sig.as_ref().to_vec()).collect();

    let static_account_keys = transaction.message.static_account_keys();
    let account_keys = static_account_keys.iter().map(|key| key.as_ref().to_vec()).collect();

    let instructions = transaction
        .message
        .instructions()
        .iter()
        .map(|inst| ProtoCompiledInstruction {
            program_id_index: inst.program_id_index as u32,
            accounts: inst.accounts.iter().map(|&idx| idx as u32).collect(),
            data: inst.data.clone(),
        })
        .collect();

    let header = transaction.message.header();
    let proto_header = ProtoMessageHeader {
        num_required_signatures: header.num_required_signatures as u32,
        num_readonly_signed_accounts: header.num_readonly_signed_accounts as u32,
        num_readonly_unsigned_accounts: header.num_readonly_unsigned_accounts as u32,
    };

    let recent_blockhash = transaction.message.recent_blockhash().as_ref().to_vec();

    let legacy_message = ProtoLegacyMessage {
        header: Some(proto_header),
        account_keys,
        recent_blockhash,
        instructions,
    };

    let message = ProtoVersionedMessage {
        message_type: Some(crate::grpc::proto::versioned_message::MessageType::Legacy(
            legacy_message,
        )),
    };

    Ok(ProtoVersionedTransaction {
        signatures,
        message: Some(message),
    })
}
