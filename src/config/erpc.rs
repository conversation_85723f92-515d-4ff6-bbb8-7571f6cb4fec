use serde::{Deserialize, Serialize};

use crate::types::{ConfigError, ConfigR<PERSON>ult};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ErpcConfig {
    pub endpoint: String,
    pub max_retries: u32,
    pub initial_retry_delay_ms: u64,
    pub max_retry_delay_ms: u64,
    pub retry_backoff_multiplier: f64,
    pub auto_reconnect: bool,
}

impl Default for ErpcConfig {
    fn default() -> Self {
        Self {
            endpoint: String::new(),
            max_retries: 5,
            initial_retry_delay_ms: 1000,
            max_retry_delay_ms: 30000,
            retry_backoff_multiplier: 2.0,
            auto_reconnect: true,
        }
    }
}

impl ErpcConfig {
    pub fn validate(&self) -> ConfigResult<()> {
        if self.endpoint.is_empty() {
            return Err(ConfigError::MissingRequired {
                key: "erpc.endpoint".to_string(),
            });
        }

        if !self.endpoint.starts_with("http://") && !self.endpoint.starts_with("https://") {
            return Err(ConfigError::InvalidValue {
                key: "erpc.endpoint".to_string(),
                value: self.endpoint.clone(),
            });
        }

        if self.max_retries == 0 {
            return Err(ConfigError::InvalidValue {
                key: "erpc.max_retries".to_string(),
                value: self.max_retries.to_string(),
            });
        }

        if self.retry_backoff_multiplier <= 1.0 {
            return Err(ConfigError::InvalidValue {
                key: "erpc.retry_backoff_multiplier".to_string(),
                value: self.retry_backoff_multiplier.to_string(),
            });
        }

        Ok(())
    }
}
