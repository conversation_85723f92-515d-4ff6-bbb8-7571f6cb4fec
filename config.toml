[erpc]
endpoint = "https://shreds-far-point.erpc.global"

[app]
environment = "development"
accounts = ["6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"]

[grpc]
bind_address = "127.0.0.1"
port = 50051
broadcast_buffer_size = 10000
tcp_nodelay = true
tcp_keepalive_seconds = 30
http2_keepalive_interval_seconds = 30
http2_keepalive_timeout_seconds = 5

[logging]
level = "info"
format = "pretty"

[performance]
enable_jemalloc = true

[performance.memory_pools]
transaction_pool_initial_size = 100
transaction_pool_max_size = 1000
buffer_pool_initial_size = 50
buffer_pool_max_size = 500
enable_pool_stats_logging = false
stats_log_interval_seconds = 60

[performance.metrics]
enable_metrics = true
enable_detailed_logging = false
metrics_log_interval_seconds = 300
enable_processing_timers = true

[performance.cpu_affinity]
enable_cpu_affinity = false
auto_detect_cores = true
